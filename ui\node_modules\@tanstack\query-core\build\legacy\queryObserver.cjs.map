{"version": 3, "sources": ["../../src/queryObserver.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { notifyManager } from './notifyManager'\nimport { fetchState } from './query'\nimport { Subscribable } from './subscribable'\nimport { pendingThenable } from './thenable'\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport type { FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { PendingThenable, Thenable } from './thenable'\nimport type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\ninterface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  #client: QueryClient\n  #currentQuery: Query<TQueryFnData, TError, TQueryData, TQueryKey> = undefined!\n  #currentQueryInitialState: QueryState<TQueryData, TError> = undefined!\n  #currentResult: QueryObserverResult<TData, TError> = undefined!\n  #currentResultState?: QueryState<TQueryData, TError>\n  #currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  #currentThenable: Thenable<TData>\n  #selectError: TError | null\n  #selectFn?: (data: TQueryData) => TData\n  #selectResult?: TData\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData?: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  #staleTimeoutId?: ReturnType<typeof setTimeout>\n  #refetchIntervalId?: ReturnType<typeof setInterval>\n  #currentRefetchInterval?: number | false\n  #trackedProps = new Set<keyof QueryObserverResult>()\n\n  constructor(\n    client: QueryClient,\n    public options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.#client = client\n    this.#selectError = null\n    this.#currentThenable = pendingThenable()\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(\n        new Error('experimental_prefetchInRender feature flag is not enabled'),\n      )\n    }\n\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch()\n      } else {\n        this.updateResult()\n      }\n\n      this.#updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.#clearStaleTimeout()\n    this.#clearRefetchInterval()\n    this.#currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.#currentQuery\n\n    this.options = this.#client.defaultQueryOptions(options)\n\n    if (\n      this.options.enabled !== undefined &&\n      typeof this.options.enabled !== 'boolean' &&\n      typeof this.options.enabled !== 'function' &&\n      typeof resolveEnabled(this.options.enabled, this.#currentQuery) !==\n        'boolean'\n    ) {\n      throw new Error(\n        'Expected enabled to be a boolean or a callback that returns a boolean',\n      )\n    }\n\n    this.#updateQuery()\n    this.#currentQuery.setOptions(this.options)\n\n    if (\n      prevOptions._defaulted &&\n      !shallowEqualObjects(this.options, prevOptions)\n    ) {\n      this.#client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.#currentQuery,\n        observer: this,\n      })\n    }\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.#currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.#executeFetch()\n    }\n\n    // Update result\n    this.updateResult()\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        resolveStaleTime(this.options.staleTime, this.#currentQuery) !==\n          resolveStaleTime(prevOptions.staleTime, this.#currentQuery))\n    ) {\n      this.#updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.#computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        nextRefetchInterval !== this.#currentRefetchInterval)\n    ) {\n      this.#updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.#client.getQueryCache().build(this.#client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult every time\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.#currentResult = result\n      this.#currentResultOptions = this.options\n      this.#currentResultState = this.#currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.#currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n    onPropTracked?: (key: keyof QueryObserverResult) => void,\n  ): QueryObserverResult<TData, TError> {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key as keyof QueryObserverResult)\n        onPropTracked?.(key as keyof QueryObserverResult)\n        return Reflect.get(target, key)\n      },\n    })\n  }\n\n  trackProp(key: keyof QueryObserverResult) {\n    this.#trackedProps.add(key)\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.#currentQuery\n  }\n\n  refetch({ ...options }: RefetchOptions = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.#client.defaultQueryOptions(options)\n\n    const query = this.#client\n      .getQueryCache()\n      .build(this.#client, defaultedOptions)\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.#currentResult\n    })\n  }\n\n  #executeFetch(\n    fetchOptions?: Omit<ObserverFetchOptions, 'initialPromise'>,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.#updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.#currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  #updateStaleTimeout(): void {\n    this.#clearStaleTimeout()\n    const staleTime = resolveStaleTime(\n      this.options.staleTime,\n      this.#currentQuery,\n    )\n\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return\n    }\n\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime)\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  #computeRefetchInterval() {\n    return (\n      (typeof this.options.refetchInterval === 'function'\n        ? this.options.refetchInterval(this.#currentQuery)\n        : this.options.refetchInterval) ?? false\n    )\n  }\n\n  #updateRefetchInterval(nextInterval: number | false): void {\n    this.#clearRefetchInterval()\n\n    this.#currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      resolveEnabled(this.options.enabled, this.#currentQuery) === false ||\n      !isValidTimeout(this.#currentRefetchInterval) ||\n      this.#currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.#refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.#executeFetch()\n      }\n    }, this.#currentRefetchInterval)\n  }\n\n  #updateTimers(): void {\n    this.#updateStaleTimeout()\n    this.#updateRefetchInterval(this.#computeRefetchInterval())\n  }\n\n  #clearStaleTimeout(): void {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId)\n      this.#staleTimeoutId = undefined\n    }\n  }\n\n  #clearRefetchInterval(): void {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId)\n      this.#refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.#currentQuery\n    const prevOptions = this.options\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.#currentResultState\n    const prevResultOptions = this.#currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.#currentQueryInitialState\n\n    const { state } = query\n    let newState = { ...state }\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options),\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        newState.fetchStatus = 'idle'\n      }\n    }\n\n    let { error, errorUpdatedAt, status } = newState\n\n    // Per default, use query data\n    data = newState.data as unknown as TData\n    let skipSelect = false\n\n    // use placeholderData if needed\n    if (\n      options.placeholderData !== undefined &&\n      data === undefined &&\n      status === 'pending'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n        // we have to skip select when reading this memoization\n        // because prevResult.data is already \"selected\"\n        skipSelect = true\n      } else {\n        // compute placeholderData\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (\n                options.placeholderData as unknown as PlaceholderDataFunction<TQueryData>\n              )(\n                this.#lastQueryWithDefinedData?.state.data,\n                this.#lastQueryWithDefinedData as any,\n              )\n            : options.placeholderData\n      }\n\n      if (placeholderData !== undefined) {\n        status = 'success'\n        data = replaceData(\n          prevResult?.data,\n          placeholderData as unknown,\n          options,\n        ) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    // Select data if needed\n    // this also runs placeholderData through the select function\n    if (options.select && data !== undefined && !skipSelect) {\n      // Memoize select result\n      if (\n        prevResult &&\n        data === prevResultState?.data &&\n        options.select === this.#selectFn\n      ) {\n        data = this.#selectResult\n      } else {\n        try {\n          this.#selectFn = options.select\n          data = options.select(data as any)\n          data = replaceData(prevResult?.data, data, options)\n          this.#selectResult = data\n          this.#selectError = null\n        } catch (selectError) {\n          this.#selectError = selectError as TError\n        }\n      }\n    }\n\n    if (this.#selectError) {\n      error = this.#selectError as any\n      data = this.#selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = newState.fetchStatus === 'fetching'\n    const isPending = status === 'pending'\n    const isError = status === 'error'\n\n    const isLoading = isPending && isFetching\n    const hasData = data !== undefined\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        newState.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === 'paused',\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable,\n      isEnabled: resolveEnabled(options.enabled, query) !== false,\n    }\n\n    const nextResult = result as QueryObserverResult<TData, TError>\n\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable: PendingThenable<TData>) => {\n        if (nextResult.status === 'error') {\n          thenable.reject(nextResult.error)\n        } else if (nextResult.data !== undefined) {\n          thenable.resolve(nextResult.data)\n        }\n      }\n\n      /**\n       * Create a new thenable and result promise when the results have changed\n       */\n      const recreateThenable = () => {\n        const pending =\n          (this.#currentThenable =\n          nextResult.promise =\n            pendingThenable())\n\n        finalizeThenableIfPossible(pending)\n      }\n\n      const prevThenable = this.#currentThenable\n      switch (prevThenable.status) {\n        case 'pending':\n          // Finalize the previous thenable if it was pending\n          // and we are still observing the same query\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable)\n          }\n          break\n        case 'fulfilled':\n          if (\n            nextResult.status === 'error' ||\n            nextResult.data !== prevThenable.value\n          ) {\n            recreateThenable()\n          }\n          break\n        case 'rejected':\n          if (\n            nextResult.status !== 'error' ||\n            nextResult.error !== prevThenable.reason\n          ) {\n            recreateThenable()\n          }\n          break\n      }\n    }\n\n    return nextResult\n  }\n\n  updateResult(): void {\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.#currentQuery, this.options)\n\n    this.#currentResultState = this.#currentQuery.state\n    this.#currentResultOptions = this.options\n\n    if (this.#currentResultState.data !== undefined) {\n      this.#lastQueryWithDefinedData = this.#currentQuery\n    }\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.#currentResult = nextResult\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.#trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps,\n      )\n\n      if (this.options.throwOnError) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey]\n\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    this.#notify({ listeners: shouldNotifyListeners() })\n  }\n\n  #updateQuery(): void {\n    const query = this.#client.getQueryCache().build(this.#client, this.options)\n\n    if (query === this.#currentQuery) {\n      return\n    }\n\n    const prevQuery = this.#currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.#currentQuery = query\n    this.#currentQueryInitialState = query.state\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(): void {\n    this.updateResult()\n\n    if (this.hasListeners()) {\n      this.#updateTimers()\n    }\n  }\n\n  #notify(notifyOptions: { listeners: boolean }): void {\n    notifyManager.batch(() => {\n      // First, trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: 'observerResultsUpdated',\n      })\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.state.data === undefined &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.data !== undefined &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: (typeof options)['refetchOnMount'] &\n    (typeof options)['refetchOnWindowFocus'] &\n    (typeof options)['refetchOnReconnect'],\n) {\n  if (\n    resolveEnabled(options.enabled, query) !== false &&\n    resolveStaleTime(options.staleTime, query) !== 'static'\n  ) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    (query !== prevQuery ||\n      resolveEnabled(prevOptions.enabled, query) === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.isStaleByTime(resolveStaleTime(options.staleTime, query))\n  )\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n) {\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAA6B;AAC7B,2BAA8B;AAC9B,mBAA2B;AAC3B,0BAA6B;AAC7B,sBAAgC;AAChC,mBASO;AAdP;AAsCO,IAAM,gBAAN,cAMG,iCAAmD;AAAA,EAyB3D,YACE,QACO,SAOP;AACA,UAAM;AARC;AAjCJ;AAOL;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAGA;AAAA;AAAA;AACA;AACA;AACA;AACA,sCAAgB,oBAAI,IAA+B;AAcjD,uBAAK,SAAU;AACf,uBAAK,cAAe;AACpB,uBAAK,sBAAmB,iCAAgB;AACxC,QAAI,CAAC,KAAK,QAAQ,+BAA+B;AAC/C,yBAAK,kBAAiB;AAAA,QACpB,IAAI,MAAM,2DAA2D;AAAA,MACvE;AAAA,IACF;AAEA,SAAK,YAAY;AACjB,SAAK,WAAW,OAAO;AAAA,EACzB;AAAA,EAEU,cAAoB;AAC5B,SAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;AAAA,EACvC;AAAA,EAEU,cAAoB;AAC5B,QAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,yBAAK,eAAc,YAAY,IAAI;AAEnC,UAAI,mBAAmB,mBAAK,gBAAe,KAAK,OAAO,GAAG;AACxD,8BAAK,2CAAL;AAAA,MACF,OAAO;AACL,aAAK,aAAa;AAAA,MACpB;AAEA,4BAAK,2CAAL;AAAA,IACF;AAAA,EACF;AAAA,EAEU,gBAAsB;AAC9B,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EAEA,yBAAkC;AAChC,WAAO;AAAA,MACL,mBAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EAEA,2BAAoC;AAClC,WAAO;AAAA,MACL,mBAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EAEA,UAAgB;AACd,SAAK,YAAY,oBAAI,IAAI;AACzB,0BAAK,gDAAL;AACA,0BAAK,mDAAL;AACA,uBAAK,eAAc,eAAe,IAAI;AAAA,EACxC;AAAA,EAEA,WACE,SAOM;AACN,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,mBAAK;AAEvB,SAAK,UAAU,mBAAK,SAAQ,oBAAoB,OAAO;AAEvD,QACE,KAAK,QAAQ,YAAY,UACzB,OAAO,KAAK,QAAQ,YAAY,aAChC,OAAO,KAAK,QAAQ,YAAY,cAChC,WAAO,6BAAe,KAAK,QAAQ,SAAS,mBAAK,cAAa,MAC5D,WACF;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,0BAAK,0CAAL;AACA,uBAAK,eAAc,WAAW,KAAK,OAAO;AAE1C,QACE,YAAY,cACZ,KAAC,kCAAoB,KAAK,SAAS,WAAW,GAC9C;AACA,yBAAK,SAAQ,cAAc,EAAE,OAAO;AAAA,QAClC,MAAM;AAAA,QACN,OAAO,mBAAK;AAAA,QACZ,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,UAAM,UAAU,KAAK,aAAa;AAGlC,QACE,WACA;AAAA,MACE,mBAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL;AAAA,IACF,GACA;AACA,4BAAK,2CAAL;AAAA,IACF;AAGA,SAAK,aAAa;AAGlB,QACE,YACC,mBAAK,mBAAkB,iBACtB,6BAAe,KAAK,QAAQ,SAAS,mBAAK,cAAa,UACrD,6BAAe,YAAY,SAAS,mBAAK,cAAa,SACxD,+BAAiB,KAAK,QAAQ,WAAW,mBAAK,cAAa,UACzD,+BAAiB,YAAY,WAAW,mBAAK,cAAa,IAC9D;AACA,4BAAK,iDAAL;AAAA,IACF;AAEA,UAAM,sBAAsB,sBAAK,qDAAL;AAG5B,QACE,YACC,mBAAK,mBAAkB,iBACtB,6BAAe,KAAK,QAAQ,SAAS,mBAAK,cAAa,UACrD,6BAAe,YAAY,SAAS,mBAAK,cAAa,KACxD,wBAAwB,mBAAK,2BAC/B;AACA,4BAAK,oDAAL,WAA4B;AAAA,IAC9B;AAAA,EACF;AAAA,EAEA,oBACE,SAOoC;AACpC,UAAM,QAAQ,mBAAK,SAAQ,cAAc,EAAE,MAAM,mBAAK,UAAS,OAAO;AAEtE,UAAM,SAAS,KAAK,aAAa,OAAO,OAAO;AAE/C,QAAI,sCAAsC,MAAM,MAAM,GAAG;AAiBvD,yBAAK,gBAAiB;AACtB,yBAAK,uBAAwB,KAAK;AAClC,yBAAK,qBAAsB,mBAAK,eAAc;AAAA,IAChD;AACA,WAAO;AAAA,EACT;AAAA,EAEA,mBAAuD;AACrD,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,YACE,QACA,eACoC;AACpC,WAAO,IAAI,MAAM,QAAQ;AAAA,MACvB,KAAK,CAAC,QAAQ,QAAQ;AACpB,aAAK,UAAU,GAAgC;AAC/C,uDAAgB;AAChB,eAAO,QAAQ,IAAI,QAAQ,GAAG;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,UAAU,KAAgC;AACxC,uBAAK,eAAc,IAAI,GAAG;AAAA,EAC5B;AAAA,EAEA,kBAAsE;AACpE,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,QAAQ,EAAE,GAAG,QAAQ,IAAoB,CAAC,GAExC;AACA,WAAO,KAAK,MAAM;AAAA,MAChB,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA,EAEA,gBACE,SAO6C;AAC7C,UAAM,mBAAmB,mBAAK,SAAQ,oBAAoB,OAAO;AAEjE,UAAM,QAAQ,mBAAK,SAChB,cAAc,EACd,MAAM,mBAAK,UAAS,gBAAgB;AAEvC,WAAO,MAAM,MAAM,EAAE,KAAK,MAAM,KAAK,aAAa,OAAO,gBAAgB,CAAC;AAAA,EAC5E;AAAA,EAEU,MACR,cAC6C;AAC7C,WAAO,sBAAK,2CAAL,WAAmB;AAAA,MACxB,GAAG;AAAA,MACH,eAAe,aAAa,iBAAiB;AAAA,IAC/C,GAAG,KAAK,MAAM;AACZ,WAAK,aAAa;AAClB,aAAO,mBAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAgGU,aACR,OACA,SAOoC;AA5axC;AA6aI,UAAM,YAAY,mBAAK;AACvB,UAAM,cAAc,KAAK;AACzB,UAAM,aAAa,mBAAK;AAGxB,UAAM,kBAAkB,mBAAK;AAC7B,UAAM,oBAAoB,mBAAK;AAC/B,UAAM,cAAc,UAAU;AAC9B,UAAM,oBAAoB,cACtB,MAAM,QACN,mBAAK;AAET,UAAM,EAAE,MAAM,IAAI;AAClB,QAAI,WAAW,EAAE,GAAG,MAAM;AAC1B,QAAI,oBAAoB;AACxB,QAAI;AAGJ,QAAI,QAAQ,oBAAoB;AAC9B,YAAM,UAAU,KAAK,aAAa;AAElC,YAAM,eAAe,CAAC,WAAW,mBAAmB,OAAO,OAAO;AAElE,YAAM,kBACJ,WAAW,sBAAsB,OAAO,WAAW,SAAS,WAAW;AAEzE,UAAI,gBAAgB,iBAAiB;AACnC,mBAAW;AAAA,UACT,GAAG;AAAA,UACH,OAAG,yBAAW,MAAM,MAAM,MAAM,OAAO;AAAA,QACzC;AAAA,MACF;AACA,UAAI,QAAQ,uBAAuB,eAAe;AAChD,iBAAS,cAAc;AAAA,MACzB;AAAA,IACF;AAEA,QAAI,EAAE,OAAO,gBAAgB,OAAO,IAAI;AAGxC,WAAO,SAAS;AAChB,QAAI,aAAa;AAGjB,QACE,QAAQ,oBAAoB,UAC5B,SAAS,UACT,WAAW,WACX;AACA,UAAI;AAGJ,WACE,yCAAY,sBACZ,QAAQ,qBAAoB,uDAAmB,kBAC/C;AACA,0BAAkB,WAAW;AAG7B,qBAAa;AAAA,MACf,OAAO;AAEL,0BACE,OAAO,QAAQ,oBAAoB,aAE7B,QAAQ;AAAA,WAER,wBAAK,+BAAL,mBAAgC,MAAM;AAAA,UACtC,mBAAK;AAAA,QACP,IACA,QAAQ;AAAA,MAChB;AAEA,UAAI,oBAAoB,QAAW;AACjC,iBAAS;AACT,mBAAO;AAAA,UACL,yCAAY;AAAA,UACZ;AAAA,UACA;AAAA,QACF;AACA,4BAAoB;AAAA,MACtB;AAAA,IACF;AAIA,QAAI,QAAQ,UAAU,SAAS,UAAa,CAAC,YAAY;AAEvD,UACE,cACA,UAAS,mDAAiB,SAC1B,QAAQ,WAAW,mBAAK,YACxB;AACA,eAAO,mBAAK;AAAA,MACd,OAAO;AACL,YAAI;AACF,6BAAK,WAAY,QAAQ;AACzB,iBAAO,QAAQ,OAAO,IAAW;AACjC,qBAAO,0BAAY,yCAAY,MAAM,MAAM,OAAO;AAClD,6BAAK,eAAgB;AACrB,6BAAK,cAAe;AAAA,QACtB,SAAS,aAAa;AACpB,6BAAK,cAAe;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,mBAAK,eAAc;AACrB,cAAQ,mBAAK;AACb,aAAO,mBAAK;AACZ,uBAAiB,KAAK,IAAI;AAC1B,eAAS;AAAA,IACX;AAEA,UAAM,aAAa,SAAS,gBAAgB;AAC5C,UAAM,YAAY,WAAW;AAC7B,UAAM,UAAU,WAAW;AAE3B,UAAM,YAAY,aAAa;AAC/B,UAAM,UAAU,SAAS;AAEzB,UAAM,SAAiD;AAAA,MACrD;AAAA,MACA,aAAa,SAAS;AAAA,MACtB;AAAA,MACA,WAAW,WAAW;AAAA,MACtB;AAAA,MACA,kBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,eAAe,SAAS;AAAA,MACxB;AAAA,MACA;AAAA,MACA,cAAc,SAAS;AAAA,MACvB,eAAe,SAAS;AAAA,MACxB,kBAAkB,SAAS;AAAA,MAC3B,WAAW,SAAS,kBAAkB,KAAK,SAAS,mBAAmB;AAAA,MACvE,qBACE,SAAS,kBAAkB,kBAAkB,mBAC7C,SAAS,mBAAmB,kBAAkB;AAAA,MAChD;AAAA,MACA,cAAc,cAAc,CAAC;AAAA,MAC7B,gBAAgB,WAAW,CAAC;AAAA,MAC5B,UAAU,SAAS,gBAAgB;AAAA,MACnC;AAAA,MACA,gBAAgB,WAAW;AAAA,MAC3B,SAAS,QAAQ,OAAO,OAAO;AAAA,MAC/B,SAAS,KAAK;AAAA,MACd,SAAS,mBAAK;AAAA,MACd,eAAW,6BAAe,QAAQ,SAAS,KAAK,MAAM;AAAA,IACxD;AAEA,UAAM,aAAa;AAEnB,QAAI,KAAK,QAAQ,+BAA+B;AAC9C,YAAM,6BAA6B,CAAC,aAAqC;AACvE,YAAI,WAAW,WAAW,SAAS;AACjC,mBAAS,OAAO,WAAW,KAAK;AAAA,QAClC,WAAW,WAAW,SAAS,QAAW;AACxC,mBAAS,QAAQ,WAAW,IAAI;AAAA,QAClC;AAAA,MACF;AAKA,YAAM,mBAAmB,MAAM;AAC7B,cAAM,UACH,mBAAK,kBACN,WAAW,cACT,iCAAgB;AAEpB,mCAA2B,OAAO;AAAA,MACpC;AAEA,YAAM,eAAe,mBAAK;AAC1B,cAAQ,aAAa,QAAQ;AAAA,QAC3B,KAAK;AAGH,cAAI,MAAM,cAAc,UAAU,WAAW;AAC3C,uCAA2B,YAAY;AAAA,UACzC;AACA;AAAA,QACF,KAAK;AACH,cACE,WAAW,WAAW,WACtB,WAAW,SAAS,aAAa,OACjC;AACA,6BAAiB;AAAA,UACnB;AACA;AAAA,QACF,KAAK;AACH,cACE,WAAW,WAAW,WACtB,WAAW,UAAU,aAAa,QAClC;AACA,6BAAiB;AAAA,UACnB;AACA;AAAA,MACJ;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,eAAqB;AACnB,UAAM,aAAa,mBAAK;AAIxB,UAAM,aAAa,KAAK,aAAa,mBAAK,gBAAe,KAAK,OAAO;AAErE,uBAAK,qBAAsB,mBAAK,eAAc;AAC9C,uBAAK,uBAAwB,KAAK;AAElC,QAAI,mBAAK,qBAAoB,SAAS,QAAW;AAC/C,yBAAK,2BAA4B,mBAAK;AAAA,IACxC;AAGA,YAAI,kCAAoB,YAAY,UAAU,GAAG;AAC/C;AAAA,IACF;AAEA,uBAAK,gBAAiB;AAEtB,UAAM,wBAAwB,MAAe;AAC3C,UAAI,CAAC,YAAY;AACf,eAAO;AAAA,MACT;AAEA,YAAM,EAAE,oBAAoB,IAAI,KAAK;AACrC,YAAM,2BACJ,OAAO,wBAAwB,aAC3B,oBAAoB,IACpB;AAEN,UACE,6BAA6B,SAC5B,CAAC,4BAA4B,CAAC,mBAAK,eAAc,MAClD;AACA,eAAO;AAAA,MACT;AAEA,YAAM,gBAAgB,IAAI;AAAA,QACxB,4BAA4B,mBAAK;AAAA,MACnC;AAEA,UAAI,KAAK,QAAQ,cAAc;AAC7B,sBAAc,IAAI,OAAO;AAAA,MAC3B;AAEA,aAAO,OAAO,KAAK,mBAAK,eAAc,EAAE,KAAK,CAAC,QAAQ;AACpD,cAAM,WAAW;AACjB,cAAM,UAAU,mBAAK,gBAAe,QAAQ,MAAM,WAAW,QAAQ;AAErE,eAAO,WAAW,cAAc,IAAI,QAAQ;AAAA,MAC9C,CAAC;AAAA,IACH;AAEA,0BAAK,qCAAL,WAAa,EAAE,WAAW,sBAAsB,EAAE;AAAA,EACpD;AAAA,EAqBA,gBAAsB;AACpB,SAAK,aAAa;AAElB,QAAI,KAAK,aAAa,GAAG;AACvB,4BAAK,2CAAL;AAAA,IACF;AAAA,EACF;AAkBF;AAnrBE;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AA7BK;AA+RL,kBAAa,SACX,cACiC;AAEjC,wBAAK,0CAAL;AAGA,MAAI,UAA2C,mBAAK,eAAc;AAAA,IAChE,KAAK;AAAA,IACL;AAAA,EACF;AAEA,MAAI,EAAC,6CAAc,eAAc;AAC/B,cAAU,QAAQ,MAAM,iBAAI;AAAA,EAC9B;AAEA,SAAO;AACT;AAEA,wBAAmB,WAAS;AAC1B,wBAAK,gDAAL;AACA,QAAM,gBAAY;AAAA,IAChB,KAAK,QAAQ;AAAA,IACb,mBAAK;AAAA,EACP;AAEA,MAAI,yBAAY,mBAAK,gBAAe,WAAW,KAAC,6BAAe,SAAS,GAAG;AACzE;AAAA,EACF;AAEA,QAAM,WAAO,6BAAe,mBAAK,gBAAe,eAAe,SAAS;AAIxE,QAAM,UAAU,OAAO;AAEvB,qBAAK,iBAAkB,WAAW,MAAM;AACtC,QAAI,CAAC,mBAAK,gBAAe,SAAS;AAChC,WAAK,aAAa;AAAA,IACpB;AAAA,EACF,GAAG,OAAO;AACZ;AAEA,4BAAuB,WAAG;AACxB,UACG,OAAO,KAAK,QAAQ,oBAAoB,aACrC,KAAK,QAAQ,gBAAgB,mBAAK,cAAa,IAC/C,KAAK,QAAQ,oBAAoB;AAEzC;AAEA,2BAAsB,SAAC,cAAoC;AACzD,wBAAK,mDAAL;AAEA,qBAAK,yBAA0B;AAE/B,MACE,6BACA,6BAAe,KAAK,QAAQ,SAAS,mBAAK,cAAa,MAAM,SAC7D,KAAC,6BAAe,mBAAK,wBAAuB,KAC5C,mBAAK,6BAA4B,GACjC;AACA;AAAA,EACF;AAEA,qBAAK,oBAAqB,YAAY,MAAM;AAC1C,QACE,KAAK,QAAQ,+BACb,iCAAa,UAAU,GACvB;AACA,4BAAK,2CAAL;AAAA,IACF;AAAA,EACF,GAAG,mBAAK,wBAAuB;AACjC;AAEA,kBAAa,WAAS;AACpB,wBAAK,iDAAL;AACA,wBAAK,oDAAL,WAA4B,sBAAK,qDAAL;AAC9B;AAEA,uBAAkB,WAAS;AACzB,MAAI,mBAAK,kBAAiB;AACxB,iBAAa,mBAAK,gBAAe;AACjC,uBAAK,iBAAkB;AAAA,EACzB;AACF;AAEA,0BAAqB,WAAS;AAC5B,MAAI,mBAAK,qBAAoB;AAC3B,kBAAc,mBAAK,mBAAkB;AACrC,uBAAK,oBAAqB;AAAA,EAC5B;AACF;AAoRA,iBAAY,WAAS;AACnB,QAAM,QAAQ,mBAAK,SAAQ,cAAc,EAAE,MAAM,mBAAK,UAAS,KAAK,OAAO;AAE3E,MAAI,UAAU,mBAAK,gBAAe;AAChC;AAAA,EACF;AAEA,QAAM,YAAY,mBAAK;AAGvB,qBAAK,eAAgB;AACrB,qBAAK,2BAA4B,MAAM;AAEvC,MAAI,KAAK,aAAa,GAAG;AACvB,2CAAW,eAAe;AAC1B,UAAM,YAAY,IAAI;AAAA,EACxB;AACF;AAUA,YAAO,SAAC,eAA6C;AACnD,qCAAc,MAAM,MAAM;AAExB,QAAI,cAAc,WAAW;AAC3B,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,mBAAK,eAAc;AAAA,MAC9B,CAAC;AAAA,IACH;AAGA,uBAAK,SAAQ,cAAc,EAAE,OAAO;AAAA,MAClC,OAAO,mBAAK;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH;AAGF,SAAS,kBACP,OACA,SACS;AACT,aACE,6BAAe,QAAQ,SAAS,KAAK,MAAM,SAC3C,MAAM,MAAM,SAAS,UACrB,EAAE,MAAM,MAAM,WAAW,WAAW,QAAQ,iBAAiB;AAEjE;AAEA,SAAS,mBACP,OACA,SACS;AACT,SACE,kBAAkB,OAAO,OAAO,KAC/B,MAAM,MAAM,SAAS,UACpB,cAAc,OAAO,SAAS,QAAQ,cAAc;AAE1D;AAEA,SAAS,cACP,OACA,SACA,OAGA;AACA,UACE,6BAAe,QAAQ,SAAS,KAAK,MAAM,aAC3C,+BAAiB,QAAQ,WAAW,KAAK,MAAM,UAC/C;AACA,UAAM,QAAQ,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AAE3D,WAAO,UAAU,YAAa,UAAU,SAAS,QAAQ,OAAO,OAAO;AAAA,EACzE;AACA,SAAO;AACT;AAEA,SAAS,sBACP,OACA,WACA,SACA,aACS;AACT,UACG,UAAU,iBACT,6BAAe,YAAY,SAAS,KAAK,MAAM,WAChD,CAAC,QAAQ,YAAY,MAAM,MAAM,WAAW,YAC7C,QAAQ,OAAO,OAAO;AAE1B;AAEA,SAAS,QACP,OACA,SACS;AACT,aACE,6BAAe,QAAQ,SAAS,KAAK,MAAM,SAC3C,MAAM,kBAAc,+BAAiB,QAAQ,WAAW,KAAK,CAAC;AAElE;AAIA,SAAS,sCAOP,UACA,kBACA;AAGA,MAAI,KAAC,kCAAoB,SAAS,iBAAiB,GAAG,gBAAgB,GAAG;AACvE,WAAO;AAAA,EACT;AAGA,SAAO;AACT;", "names": []}